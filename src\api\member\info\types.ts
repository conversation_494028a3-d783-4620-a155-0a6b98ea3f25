export interface InfoVO {
  /**
   * 会员ID
   */
  id: string | number;

  /**
   * 会员编号（唯一）
   */
  memberNo: string;

  /**
   * 昵称
   */
  nickname: string;

  /**
   * 真实姓名
   */
  realName: string;

  /**
   * 手机号
   */
  phone: string;

  /**
   * 邮箱
   */
  email: string;

  /**
   * 头像URL
   */
  avatar: string;

  /**
   * 性别：0-未知，1-男，2-女
   */
  gender: string;

  /**
   * 生日
   */
  birthday: string;

  /**
   * 省份
   */
  province: string;

  /**
   * 城市
   */
  city: string;

  /**
   * 区县
   */
  district: string;

  /**
   * 详细地址
   */
  address: string;

  /**
   * 登录密码
   */
  password: string;

  /**
   * 密码盐值
   */
  salt: string;

  /**
   * 注册渠道：APP, WEB, WECHAT, ALIPAY
   */
  registerChannel: string;

  /**
   * 注册IP
   */
  registerIp: string;

  /**
   * 状态：0-正常，1-禁用，2-注销
   */
  status: string;

  /**
   * 备注
   */
  remark: string;
}

export interface InfoForm extends BaseEntity {
  /**
   * 会员ID
   */
  id?: string | number;

  /**
   * 会员编号（唯一）
   */
  memberNo?: string;

  /**
   * 昵称
   */
  nickname?: string;

  /**
   * 真实姓名
   */
  realName?: string;

  /**
   * 手机号
   */
  phone?: string;

  /**
   * 邮箱
   */
  email?: string;

  /**
   * 头像URL
   */
  avatar?: string;

  /**
   * 性别：0-未知，1-男，2-女
   */
  gender?: string;

  /**
   * 生日
   */
  birthday?: string;

  /**
   * 省份
   */
  province?: string;

  /**
   * 城市
   */
  city?: string;

  /**
   * 区县
   */
  district?: string;

  /**
   * 详细地址
   */
  address?: string;

  /**
   * 登录密码
   */
  password?: string;

  /**
   * 密码盐值
   */
  salt?: string;

  /**
   * 注册渠道：APP, WEB, WECHAT, ALIPAY
   */
  registerChannel?: string;

  /**
   * 注册IP
   */
  registerIp?: string;

  /**
   * 状态：0-正常，1-禁用，2-注销
   */
  status?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface InfoQuery extends PageQuery {
  /**
   * 会员编号（唯一）
   */
  memberNo?: string;

  /**
   * 昵称
   */
  nickname?: string;

  /**
   * 真实姓名
   */
  realName?: string;

  /**
   * 手机号
   */
  phone?: string;

  /**
   * 邮箱
   */
  email?: string;

  /**
   * 头像URL
   */
  avatar?: string;

  /**
   * 性别：0-未知，1-男，2-女
   */
  gender?: string;

  /**
   * 生日
   */
  birthday?: string;

  /**
   * 省份
   */
  province?: string;

  /**
   * 城市
   */
  city?: string;

  /**
   * 区县
   */
  district?: string;

  /**
   * 详细地址
   */
  address?: string;

  /**
   * 登录密码
   */
  password?: string;

  /**
   * 密码盐值
   */
  salt?: string;

  /**
   * 注册渠道：APP, WEB, WECHAT, ALIPAY
   */
  registerChannel?: string;

  /**
   * 注册IP
   */
  registerIp?: string;

  /**
   * 状态：0-正常，1-禁用，2-注销
   */
  status?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
