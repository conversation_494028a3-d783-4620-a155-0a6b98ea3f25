<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="会员名称" prop="memberId">
              <el-select v-model="queryParams.memberId" filterable clearable placeholder="请选择会员">
                <el-option v-for="member in memberList" :key="member.id" :label="member.nickname" :value="member.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="订单号" prop="orderNo">
              <el-input v-model="queryParams.orderNo" placeholder="请输入关联订单号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['member:walletDetail:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['member:walletDetail:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['member:walletDetail:remove']"
              >删除</el-button
            >
          </el-col>

          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="walletDetailList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键ID" align="center" prop="id" v-if="true" />
        <el-table-column label="会员名称" align="center" prop="memberName" />
        <el-table-column label="关联订单号" align="center" prop="orderNo" />
        <el-table-column label="业务类型" align="center" prop="businessType">
          <template #default="scope">
            <span>{{ getBusinessTypeText(scope.row.businessType) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="金额类型" align="center" prop="amountType">
          <template #default="scope">
            <span>{{ getAmountTypeText(scope.row.amountType) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="变动金额" align="center" prop="changeAmount" />
        <el-table-column label="变动前余额" align="center" prop="balanceBefore" />
        <el-table-column label="变动后余额" align="center" prop="balanceAfter" />
        <el-table-column label="变动前充值余额" align="center" prop="rechargeBalanceBefore" />
        <el-table-column label="变动后充值余额" align="center" prop="rechargeBalanceAfter" />
        <el-table-column label="变动前赠送余额" align="center" prop="bonusBalanceBefore" />
        <el-table-column label="变动后赠送余额" align="center" prop="bonusBalanceAfter" />
        <el-table-column label="关联活动ID" align="center" prop="activityId" />
        <el-table-column label="备注说明" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['member:walletDetail:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['member:walletDetail:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改钱包流水记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="walletDetailFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="会员ID" prop="memberId">
          <el-input v-model="form.memberId" placeholder="请输入会员ID" />
        </el-form-item>
        <el-form-item label="订单号" prop="orderNo">
          <el-input v-model="form.orderNo" placeholder="请输入关联订单号" />
        </el-form-item>
        <el-form-item label="变动金额" prop="changeAmount">
          <el-input v-model="form.changeAmount" placeholder="请输入变动金额" />
        </el-form-item>
        <el-form-item label="变动前余额" prop="balanceBefore">
          <el-input v-model="form.balanceBefore" placeholder="请输入变动前余额" />
        </el-form-item>
        <el-form-item label="变动后余额" prop="balanceAfter">
          <el-input v-model="form.balanceAfter" placeholder="请输入变动后余额" />
        </el-form-item>
        <el-form-item label="变动前充值余额" prop="rechargeBalanceBefore">
          <el-input v-model="form.rechargeBalanceBefore" placeholder="请输入变动前充值余额" />
        </el-form-item>
        <el-form-item label="变动后充值余额" prop="rechargeBalanceAfter">
          <el-input v-model="form.rechargeBalanceAfter" placeholder="请输入变动后充值余额" />
        </el-form-item>
        <el-form-item label="变动前赠送余额" prop="bonusBalanceBefore">
          <el-input v-model="form.bonusBalanceBefore" placeholder="请输入变动前赠送余额" />
        </el-form-item>
        <el-form-item label="变动后赠送余额" prop="bonusBalanceAfter">
          <el-input v-model="form.bonusBalanceAfter" placeholder="请输入变动后赠送余额" />
        </el-form-item>
        <el-form-item label="关联活动ID" prop="activityId">
          <el-input v-model="form.activityId" placeholder="请输入关联活动ID" />
        </el-form-item>
        <el-form-item label="备注说明" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="WalletDetail" lang="ts">
import { listWalletDetail, getWalletDetail, delWalletDetail, addWalletDetail, updateWalletDetail } from '@/api/member/walletDetail';
import { WalletDetailVO, WalletDetailQuery, WalletDetailForm } from '@/api/member/walletDetail/types';
import { listInfo } from '@/api/member/info';
import { InfoVO } from '@/api/member/info/types';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const walletDetailList = ref<WalletDetailVO[]>([]);
const memberList = ref<InfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const walletDetailFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: WalletDetailForm = {
  id: undefined,
  memberId: undefined,
  orderNo: undefined,
  businessType: undefined,
  amountType: undefined,
  changeAmount: undefined,
  balanceBefore: undefined,
  balanceAfter: undefined,
  rechargeBalanceBefore: undefined,
  rechargeBalanceAfter: undefined,
  bonusBalanceBefore: undefined,
  bonusBalanceAfter: undefined,
  activityId: undefined,
  remark: undefined
};
const data = reactive<PageData<WalletDetailForm, WalletDetailQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    memberId: undefined,
    orderNo: undefined,
    businessType: undefined,
    amountType: undefined,
    changeAmount: undefined,
    balanceBefore: undefined,
    balanceAfter: undefined,
    rechargeBalanceBefore: undefined,
    rechargeBalanceAfter: undefined,
    bonusBalanceBefore: undefined,
    bonusBalanceAfter: undefined,
    activityId: undefined,
    params: {}
  },
  rules: {
    orderNo: [{ required: true, message: '关联订单号不能为空', trigger: 'blur' }],
    activityId: [{ required: true, message: '关联活动ID不能为空', trigger: 'blur' }],
    remark: [{ required: true, message: '备注说明不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询钱包流水记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listWalletDetail(queryParams.value);
  walletDetailList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  walletDetailFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: WalletDetailVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加钱包流水记录';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: WalletDetailVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getWalletDetail(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改钱包流水记录';
};

/** 提交按钮 */
const submitForm = () => {
  walletDetailFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateWalletDetail(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addWalletDetail(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: WalletDetailVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除钱包流水记录编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delWalletDetail(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'member/walletDetail/export',
    {
      ...queryParams.value
    },
    `walletDetail_${new Date().getTime()}.xlsx`
  );
};

/** 获取业务类型文本 */
const getBusinessTypeText = (type: string | number) => {
  const typeMap: Record<string, string> = {
    '1': '充值',
    '2': '消费',
    '3': '退款',
    '4': '转账',
    '5': '活动赠送',
    '6': '管理员调整'
  };
  return typeMap[type as string] || '未知类型';
};

/** 获取金额类型文本 */
const getAmountTypeText = (type: string | number) => {
  const typeMap: Record<string, string> = {
    '1': '充值金额',
    '2': '赠送金额'
  };
  return typeMap[type as string] || '未知类型';
};

/** 查询会员列表 */
const getMemberList = async () => {
  const res = await listInfo({ pageNum: 1, pageSize: 1000 });
  memberList.value = res.rows;
};

onMounted(() => {
  getList();
  getMemberList();
});
</script>
