import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { StrategyConfigVO, StrategyConfigForm, StrategyConfigQuery } from '@/api/member/strategyConfig/types';

/**
 * 查询消费策略配置列表
 * @param query
 * @returns {*}
 */

export const listStrategyConfig = (query?: StrategyConfigQuery): AxiosPromise<StrategyConfigVO[]> => {
  return request({
    url: '/member/strategyConfig/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询消费策略配置详细
 * @param id
 */
export const getStrategyConfig = (id: string | number): AxiosPromise<StrategyConfigVO> => {
  return request({
    url: '/member/strategyConfig/' + id,
    method: 'get'
  });
};

/**
 * 新增消费策略配置
 * @param data
 */
export const addStrategyConfig = (data: StrategyConfigForm) => {
  return request({
    url: '/member/strategyConfig',
    method: 'post',
    data: data
  });
};

/**
 * 修改消费策略配置
 * @param data
 */
export const updateStrategyConfig = (data: StrategyConfigForm) => {
  return request({
    url: '/member/strategyConfig',
    method: 'put',
    data: data
  });
};

/**
 * 删除消费策略配置
 * @param id
 */
export const delStrategyConfig = (id: string | number | Array<string | number>) => {
  return request({
    url: '/member/strategyConfig/' + id,
    method: 'delete'
  });
};
