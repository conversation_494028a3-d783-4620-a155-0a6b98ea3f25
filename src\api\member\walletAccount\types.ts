export interface WalletAccountVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 会员ID
   */
  memberId: string | number;

  /**
   * 会员名称
   */
  memberName: string;

  /**
   * 总余额
   */
  totalBalance: number;

  /**
   * 充值余额（用户实际付费）
   */
  rechargeBalance: number;

  /**
   * 赠送余额（平台赠送）
   */
  bonusBalance: number;

  /**
   * 冻结余额
   */
  frozenBalance: number;
}

export interface WalletAccountForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 会员ID
   */
  memberId?: string | number;

  /**
   * 会员名称
   */
  memberName?: string;

  /**
   * 总余额
   */
  totalBalance?: number;

  /**
   * 充值余额（用户实际付费）
   */
  rechargeBalance?: number;

  /**
   * 赠送余额（平台赠送）
   */
  bonusBalance?: number;

  /**
   * 冻结余额
   */
  frozenBalance?: number;
}

export interface WalletAccountQuery extends PageQuery {
  /**
   * 会员ID
   */
  memberId?: string | number;

  /**
   * 会员名称
   */
  memberName?: string;

  /**
   * 总余额
   */
  totalBalance?: number;

  /**
   * 充值余额（用户实际付费）
   */
  rechargeBalance?: number;

  /**
   * 赠送余额（平台赠送）
   */
  bonusBalance?: number;

  /**
   * 冻结余额
   */
  frozenBalance?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
