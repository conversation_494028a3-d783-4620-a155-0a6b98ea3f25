<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="会员名称" prop="memberId">
              <el-select v-model="queryParams.memberId" filterable clearable placeholder="请选择会员">
                <el-option v-for="member in memberList" :key="member.id" :label="member.nickname" :value="member.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="关联订单号" prop="orderNo">
              <el-input v-model="queryParams.orderNo" placeholder="请输入关联订单号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <!-- <el-form-item label="积分变动数量" prop="pointsChange">
              <el-input v-model="queryParams.pointsChange" placeholder="请输入积分变动数量" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="变动前积分余额" prop="pointsBefore">
              <el-input v-model="queryParams.pointsBefore" placeholder="请输入变动前积分余额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="变动后积分余额" prop="pointsAfter">
              <el-input v-model="queryParams.pointsAfter" placeholder="请输入变动后积分余额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="消费金额" prop="consumeAmount">
              <el-input v-model="queryParams.consumeAmount" placeholder="请输入消费金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="积分过期时间" prop="expireTime">
              <el-date-picker clearable
                v-model="queryParams.expireTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择积分过期时间"
              />
            </el-form-item> -->
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              v-hasPermi="['member:pointsDetail:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['member:pointsDetail:remove']">删除</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="pointsDetailList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键ID" align="center" prop="id" v-if="true" />
        <el-table-column label="会员名称" align="center" prop="memberName" />
        <el-table-column label="关联订单号" align="center" prop="orderNo" />
        <el-table-column label="业务类型" align="center" prop="businessType">
          <template #default="scope">
            <span>{{ getBusinessTypeText(scope.row.businessType) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="积分变动数量" align="center" prop="pointsChange" />
        <el-table-column label="变动前积分余额" align="center" prop="pointsBefore" />
        <el-table-column label="变动后积分余额" align="center" prop="pointsAfter" />
        <el-table-column label="关联规则" align="center" prop="ruleName" />
        <el-table-column label="消费金额" align="center" prop="consumeAmount" />
        <!-- <el-table-column label="积分过期时间" align="center" prop="expireTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.expireTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column> -->
        <el-table-column label="备注说明" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['member:pointsDetail:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['member:pointsDetail:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改积分明细记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="800px" append-to-body>
      <el-form ref="pointsDetailFormRef" :model="form" :rules="rules" label-width="110px">
        <el-form-item label="关联订单号" prop="orderNo">
          <el-input v-model="form.orderNo" placeholder="请输入关联订单号" />
        </el-form-item>
        <el-form-item label="积分变动数量" prop="pointsChange">
          <el-input v-model="form.pointsChange" placeholder="请输入积分变动数量" />
        </el-form-item>
        <el-form-item label="变动前积分余额" prop="pointsBefore">
          <el-input v-model="form.pointsBefore" placeholder="请输入变动前积分余额" />
        </el-form-item>
        <el-form-item label="变动后积分余额" prop="pointsAfter">
          <el-input v-model="form.pointsAfter" placeholder="请输入变动后积分余额" />
        </el-form-item>
        <el-form-item label="消费金额" prop="consumeAmount">
          <el-input v-model="form.consumeAmount" placeholder="请输入消费金额" />
        </el-form-item>
        <!-- <el-form-item label="积分过期时间" prop="expireTime">
          <el-date-picker clearable v-model="form.expireTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择积分过期时间">
          </el-date-picker>
        </el-form-item> -->
        <el-form-item label="备注说明" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PointsDetail" lang="ts">
import { listPointsDetail, getPointsDetail, delPointsDetail, addPointsDetail, updatePointsDetail } from '@/api/member/pointsDetail';
import { PointsDetailVO, PointsDetailQuery, PointsDetailForm } from '@/api/member/pointsDetail/types';
import { listInfo } from '@/api/member/info';
import { InfoVO } from '@/api/member/info/types';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const pointsDetailList = ref<PointsDetailVO[]>([]);
const memberList = ref<InfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const pointsDetailFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: PointsDetailForm = {
  id: undefined,
  memberId: undefined,
  orderNo: undefined,
  businessType: undefined,
  pointsChange: undefined,
  pointsBefore: undefined,
  pointsAfter: undefined,
  ruleId: undefined,
  consumeAmount: undefined,
  expireTime: undefined,
  remark: undefined,
}
const data = reactive<PageData<PointsDetailForm, PointsDetailQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    memberId: undefined,
    orderNo: undefined,
    businessType: undefined,
    pointsChange: undefined,
    pointsBefore: undefined,
    pointsAfter: undefined,
    ruleId: undefined,
    consumeAmount: undefined,
    expireTime: undefined,
    params: {
    }
  },
  rules: {
    memberId: [
      { required: true, message: "会员ID不能为空", trigger: "change" }
    ],
    orderNo: [
      { required: true, message: "关联订单号不能为空", trigger: "blur" }
    ],
    businessType: [
      { required: true, message: "业务类型：1-消费获得，2-签到获得，3-活动获得，4-积分过期，5-管理员调整不能为空", trigger: "change" }
    ],
    ruleId: [
      { required: true, message: "关联规则ID不能为空", trigger: "change" }
    ],
    consumeAmount: [
      { required: true, message: "消费金额不能为空", trigger: "blur" }
    ],
    remark: [
      { required: true, message: "备注说明不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询积分明细记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPointsDetail(queryParams.value);
  pointsDetailList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  pointsDetailFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: PointsDetailVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加积分明细记录";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: PointsDetailVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getPointsDetail(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改积分明细记录";
}

/** 提交按钮 */
const submitForm = () => {
  pointsDetailFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updatePointsDetail(form.value).finally(() => buttonLoading.value = false);
      } else {
        await addPointsDetail(form.value).finally(() => buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: PointsDetailVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除积分明细记录编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delPointsDetail(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('member/pointsDetail/export', {
    ...queryParams.value
  }, `pointsDetail_${new Date().getTime()}.xlsx`)
}

/** 获取业务类型文本 */
const getBusinessTypeText = (type: string | number) => {
  const typeMap: Record<number, string> = {
    1: '消费获得',
    2: '签到获得',
    3: '活动获得',
    4: '积分过期',
    5: '管理员调整'
  };
  return typeMap[type as number] || '未知类型';
};

/** 查询会员列表 */
const getMemberList = async () => {
  const res = await listInfo({ pageNum: 1, pageSize: 1000 });
  memberList.value = res.rows;
}

onMounted(() => {
  getList();
  getMemberList();
});
</script>
