<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="规则名称" prop="ruleName">
              <el-input v-model="queryParams.ruleName" placeholder="请输入规则名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="最小消费金额" prop="minAmount">
              <el-input v-model="queryParams.minAmount" placeholder="请输入最小消费金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="最大消费金额" prop="maxAmount">
              <el-input v-model="queryParams.maxAmount" placeholder="请输入最大消费金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="优惠值" prop="discountValue">
              <el-input v-model="queryParams.discountValue" placeholder="请输入优惠值" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="最大优惠金额" prop="maxDiscountAmount">
              <el-input v-model="queryParams.maxDiscountAmount" placeholder="请输入最大优惠金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="适用商品" prop="applicableProducts">
              <el-input v-model="queryParams.applicableProducts" placeholder="请输入适用商品" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="排除商品" prop="excludeProducts">
              <el-input v-model="queryParams.excludeProducts" placeholder="请输入排除商品" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="每人限用次数" prop="userLimitCount">
              <el-input v-model="queryParams.userLimitCount" placeholder="请输入每人限用次数" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="总使用次数限制" prop="totalLimitCount">
              <el-input v-model="queryParams.totalLimitCount" placeholder="请输入总使用次数限制" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="已使用次数" prop="usedCount">
              <el-input v-model="queryParams.usedCount" placeholder="请输入已使用次数" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="规则生效开始时间" prop="startTime">
              <el-date-picker clearable v-model="queryParams.startTime" type="date" value-format="YYYY-MM-DD" placeholder="请选择规则生效开始时间" />
            </el-form-item>
            <el-form-item label="规则生效结束时间" prop="endTime">
              <el-date-picker clearable v-model="queryParams.endTime" type="date" value-format="YYYY-MM-DD" placeholder="请选择规则生效结束时间" />
            </el-form-item>
            <el-form-item label="优先级" prop="priority">
              <el-input v-model="queryParams.priority" placeholder="请输入优先级" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否可叠加：0-不可叠加，1-可叠加" prop="canStack">
              <el-input v-model="queryParams.canStack" placeholder="请输入是否可叠加：0-不可叠加，1-可叠加" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['member:rule:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['member:rule:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['member:rule:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['member:rule:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="ruleList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键ID" align="center" prop="id" v-if="true" />
        <el-table-column label="规则名称" align="center" prop="ruleName" />
        <el-table-column label="规则类型：1-满减，2-折扣，3-买赠，4-阶梯满减" align="center" prop="ruleType" />
        <el-table-column label="最小消费金额" align="center" prop="minAmount" />
        <el-table-column label="最大消费金额" align="center" prop="maxAmount" />
        <el-table-column label="优惠值" align="center" prop="discountValue" />
        <el-table-column label="最大优惠金额" align="center" prop="maxDiscountAmount" />
        <el-table-column label="适用商品" align="center" prop="applicableProducts" />
        <el-table-column label="排除商品" align="center" prop="excludeProducts" />
        <el-table-column label="用户限制类型：0-无限制，1-每人限用，2-新用户专享" align="center" prop="userLimitType" />
        <el-table-column label="每人限用次数" align="center" prop="userLimitCount" />
        <el-table-column label="总使用次数限制" align="center" prop="totalLimitCount" />
        <el-table-column label="已使用次数" align="center" prop="usedCount" />
        <el-table-column label="规则生效开始时间" align="center" prop="startTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="规则生效结束时间" align="center" prop="endTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态：0-启用，1-禁用" align="center" prop="status" />
        <el-table-column label="优先级" align="center" prop="priority" />
        <el-table-column label="是否可叠加：0-不可叠加，1-可叠加" align="center" prop="canStack" />
        <el-table-column label="备注说明" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['member:rule:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['member:rule:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改优惠规则配置对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="ruleFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="规则名称" prop="ruleName">
          <el-input v-model="form.ruleName" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="最小消费金额" prop="minAmount">
          <el-input v-model="form.minAmount" placeholder="请输入最小消费金额" />
        </el-form-item>
        <el-form-item label="最大消费金额" prop="maxAmount">
          <el-input v-model="form.maxAmount" placeholder="请输入最大消费金额" />
        </el-form-item>
        <el-form-item label="优惠值" prop="discountValue">
          <el-input v-model="form.discountValue" placeholder="请输入优惠值" />
        </el-form-item>
        <el-form-item label="最大优惠金额" prop="maxDiscountAmount">
          <el-input v-model="form.maxDiscountAmount" placeholder="请输入最大优惠金额" />
        </el-form-item>
        <el-form-item label="适用商品" prop="applicableProducts">
          <el-input v-model="form.applicableProducts" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="排除商品" prop="excludeProducts">
          <el-input v-model="form.excludeProducts" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="每人限用次数" prop="userLimitCount">
          <el-input v-model="form.userLimitCount" placeholder="请输入每人限用次数" />
        </el-form-item>
        <el-form-item label="总使用次数限制" prop="totalLimitCount">
          <el-input v-model="form.totalLimitCount" placeholder="请输入总使用次数限制" />
        </el-form-item>
        <el-form-item label="已使用次数" prop="usedCount">
          <el-input v-model="form.usedCount" placeholder="请输入已使用次数" />
        </el-form-item>
        <el-form-item label="规则生效开始时间" prop="startTime">
          <el-date-picker clearable v-model="form.startTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择规则生效开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="规则生效结束时间" prop="endTime">
          <el-date-picker clearable v-model="form.endTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择规则生效结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-input v-model="form.priority" placeholder="请输入优先级" />
        </el-form-item>
        <el-form-item label="是否可叠加：0-不可叠加，1-可叠加" prop="canStack">
          <el-input v-model="form.canStack" placeholder="请输入是否可叠加：0-不可叠加，1-可叠加" />
        </el-form-item>
        <el-form-item label="备注说明" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Rule" lang="ts">
import { listRule, getRule, delRule, addRule, updateRule } from '@/api/member/rule';
import { RuleVO, RuleQuery, RuleForm } from '@/api/member/rule/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const ruleList = ref<RuleVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const ruleFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: RuleForm = {
  id: undefined,
  ruleName: undefined,
  ruleType: undefined,
  minAmount: undefined,
  maxAmount: undefined,
  discountValue: undefined,
  maxDiscountAmount: undefined,
  applicableProducts: undefined,
  excludeProducts: undefined,
  userLimitType: undefined,
  userLimitCount: undefined,
  totalLimitCount: undefined,
  usedCount: undefined,
  startTime: undefined,
  endTime: undefined,
  status: undefined,
  priority: undefined,
  canStack: undefined,
  remark: undefined
};
const data = reactive<PageData<RuleForm, RuleQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    ruleName: undefined,
    ruleType: undefined,
    minAmount: undefined,
    maxAmount: undefined,
    discountValue: undefined,
    maxDiscountAmount: undefined,
    applicableProducts: undefined,
    excludeProducts: undefined,
    userLimitType: undefined,
    userLimitCount: undefined,
    totalLimitCount: undefined,
    usedCount: undefined,
    startTime: undefined,
    endTime: undefined,
    status: undefined,
    priority: undefined,
    canStack: undefined,
    params: {}
  },
  rules: {
    maxAmount: [{ required: true, message: '最大消费金额不能为空', trigger: 'blur' }],
    maxDiscountAmount: [{ required: true, message: '最大优惠金额不能为空', trigger: 'blur' }],
    applicableProducts: [{ required: true, message: '适用商品不能为空', trigger: 'blur' }],
    excludeProducts: [{ required: true, message: '排除商品不能为空', trigger: 'blur' }],
    userLimitType: [{ required: true, message: '用户限制类型：0-无限制，1-每人限用，2-新用户专享不能为空', trigger: 'change' }],
    userLimitCount: [{ required: true, message: '每人限用次数不能为空', trigger: 'blur' }],
    totalLimitCount: [{ required: true, message: '总使用次数限制不能为空', trigger: 'blur' }],
    usedCount: [{ required: true, message: '已使用次数不能为空', trigger: 'blur' }],
    startTime: [{ required: true, message: '规则生效开始时间不能为空', trigger: 'blur' }],
    endTime: [{ required: true, message: '规则生效结束时间不能为空', trigger: 'blur' }],
    canStack: [{ required: true, message: '是否可叠加：0-不可叠加，1-可叠加不能为空', trigger: 'blur' }],
    remark: [{ required: true, message: '备注说明不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询优惠规则配置列表 */
const getList = async () => {
  loading.value = true;
  const res = await listRule(queryParams.value);
  ruleList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  ruleFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: RuleVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加优惠规则配置';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: RuleVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getRule(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改优惠规则配置';
};

/** 提交按钮 */
const submitForm = () => {
  ruleFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateRule(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addRule(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: RuleVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除优惠规则配置编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delRule(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'member/rule/export',
    {
      ...queryParams.value
    },
    `rule_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
