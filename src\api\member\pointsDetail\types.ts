export interface PointsDetailVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 会员ID
   */
  memberId: string | number;

  /**
   * 关联订单号
   */
  orderNo: string;

  /**
   * 业务类型：1-消费获得，2-签到获得，3-活动获得，4-积分过期，5-管理员调整
   */
  businessType: number;

  /**
   * 积分变动数量（正数为增加，负数为减少）
   */
  pointsChange: number;

  /**
   * 变动前积分余额
   */
  pointsBefore: number;

  /**
   * 变动后积分余额
   */
  pointsAfter: number;

  /**
   * 关联规则ID
   */
  ruleId: string | number;

  /**
   * 消费金额
   */
  consumeAmount: number;

  /**
   * 积分过期时间（可选）
   */
  expireTime: string;

  /**
   * 备注说明
   */
  remark: string;
}

export interface PointsDetailForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 会员ID
   */
  memberId?: string | number;

  /**
   * 关联订单号
   */
  orderNo?: string;

  /**
   * 业务类型：1-消费获得，2-签到获得，3-活动获得，4-积分过期，5-管理员调整
   */
  businessType?: number;

  /**
   * 积分变动数量（正数为增加，负数为减少）
   */
  pointsChange?: number;

  /**
   * 变动前积分余额
   */
  pointsBefore?: number;

  /**
   * 变动后积分余额
   */
  pointsAfter?: number;

  /**
   * 关联规则ID
   */
  ruleId?: string | number;

  /**
   * 消费金额
   */
  consumeAmount?: number;

  /**
   * 积分过期时间（可选）
   */
  expireTime?: string;

  /**
   * 备注说明
   */
  remark?: string;
}

export interface PointsDetailQuery extends PageQuery {
  /**
   * 会员ID
   */
  memberId?: string | number;

  /**
   * 关联订单号
   */
  orderNo?: string;

  /**
   * 业务类型：1-消费获得，2-签到获得，3-活动获得，4-积分过期，5-管理员调整
   */
  businessType?: number;

  /**
   * 积分变动数量（正数为增加，负数为减少）
   */
  pointsChange?: number;

  /**
   * 变动前积分余额
   */
  pointsBefore?: number;

  /**
   * 变动后积分余额
   */
  pointsAfter?: number;

  /**
   * 关联规则ID
   */
  ruleId?: string | number;

  /**
   * 消费金额
   */
  consumeAmount?: number;

  /**
   * 积分过期时间（可选）
   */
  expireTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
