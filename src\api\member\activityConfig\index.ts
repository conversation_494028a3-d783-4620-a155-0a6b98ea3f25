import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ActivityConfigVO, ActivityConfigForm, ActivityConfigQuery } from '@/api/member/activityConfig/types';

/**
 * 查询充值活动配置列表
 * @param query
 * @returns {*}
 */

export const listActivityConfig = (query?: ActivityConfigQuery): AxiosPromise<ActivityConfigVO[]> => {
  return request({
    url: '/member/activityConfig/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询充值活动配置详细
 * @param id
 */
export const getActivityConfig = (id: string | number): AxiosPromise<ActivityConfigVO> => {
  return request({
    url: '/member/activityConfig/' + id,
    method: 'get'
  });
};

/**
 * 新增充值活动配置
 * @param data
 */
export const addActivityConfig = (data: ActivityConfigForm) => {
  return request({
    url: '/member/activityConfig',
    method: 'post',
    data: data
  });
};

/**
 * 修改充值活动配置
 * @param data
 */
export const updateActivityConfig = (data: ActivityConfigForm) => {
  return request({
    url: '/member/activityConfig',
    method: 'put',
    data: data
  });
};

/**
 * 删除充值活动配置
 * @param id
 */
export const delActivityConfig = (id: string | number | Array<string | number>) => {
  return request({
    url: '/member/activityConfig/' + id,
    method: 'delete'
  });
};
