import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { WalletAccountVO, WalletAccountForm, WalletAccountQuery } from '@/api/member/walletAccount/types';

/**
 * 查询会员钱包账户列表
 * @param query
 * @returns {*}
 */

export const listWalletAccount = (query?: WalletAccountQuery): AxiosPromise<WalletAccountVO[]> => {
  return request({
    url: '/member/walletAccount/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询会员钱包账户详细
 * @param id
 */
export const getWalletAccount = (id: string | number): AxiosPromise<WalletAccountVO> => {
  return request({
    url: '/member/walletAccount/' + id,
    method: 'get'
  });
};

/**
 * 新增会员钱包账户
 * @param data
 */
export const addWalletAccount = (data: WalletAccountForm) => {
  return request({
    url: '/member/walletAccount',
    method: 'post',
    data: data
  });
};

/**
 * 修改会员钱包账户
 * @param data
 */
export const updateWalletAccount = (data: WalletAccountForm) => {
  return request({
    url: '/member/walletAccount',
    method: 'put',
    data: data
  });
};

/**
 * 删除会员钱包账户
 * @param id
 */
export const delWalletAccount = (id: string | number | Array<string | number>) => {
  return request({
    url: '/member/walletAccount/' + id,
    method: 'delete'
  });
};

/**
 * 会员钱包充值
 * @param data
 */
export const rechargeWallet = (data: { memberId: string | number; rechargeAmount: number; paymentMethod: string }) => {
  return request({
    url: '/member/walletAccount/recharge',
    method: 'post',
    data: data
  });
};

/**
 * 会员钱包消费
 * @param data
 */
export const consumeWallet = (data: { memberId: string | number; consumeAmount: number; orderNo: string }) => {
  return request({
    url: '/member/walletAccount/consume',
    method: 'post',
    data: data
  });
};
