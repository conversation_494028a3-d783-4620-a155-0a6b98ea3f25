<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="会员编号" prop="memberNo">
              <el-input v-model="queryParams.memberNo" placeholder="请输入会员编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="昵称" prop="nickname">
              <el-input v-model="queryParams.nickname" placeholder="请输入昵称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="真实姓名" prop="realName">
              <el-input v-model="queryParams.realName" placeholder="请输入真实姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="queryParams.phone" placeholder="请输入手机号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="queryParams.email" placeholder="请输入邮箱" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <!-- <el-form-item label="头像URL" prop="avatar">
              <el-input v-model="queryParams.avatar" placeholder="请输入头像URL" clearable @keyup.enter="handleQuery" />
            </el-form-item> -->
            <el-form-item label="性别" prop="gender">
              <el-select v-model="queryParams.gender" placeholder="请选择性别" clearable>
                <el-option label="未知" :value="0" />
                <el-option label="男" :value="1" />
                <el-option label="女" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item label="生日" prop="birthday">
              <el-date-picker clearable v-model="queryParams.birthday" type="date" value-format="YYYY-MM-DD" placeholder="请选择生日" />
            </el-form-item>
            <el-form-item label="省份" prop="province">
              <el-input v-model="queryParams.province" placeholder="请输入省份" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="城市" prop="city">
              <el-input v-model="queryParams.city" placeholder="请输入城市" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="区县" prop="district">
              <el-input v-model="queryParams.district" placeholder="请输入区县" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <!-- <el-form-item label="注册渠道" prop="registerChannel">
              <el-input v-model="queryParams.registerChannel" placeholder="请输入注册渠道：APP, WEB, WECHAT, ALIPAY" clearable @keyup.enter="handleQuery" />
            </el-form-item> -->
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['member:info:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['member:info:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['member:info:remove']"
              >删除</el-button
            >
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['member:info:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="infoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="会员ID" align="center" prop="id" v-if="true" />
        <el-table-column label="会员编号" align="center" prop="memberNo" />
        <el-table-column label="昵称" align="center" prop="nickname" />
        <el-table-column label="真实姓名" align="center" prop="realName" />
        <el-table-column label="手机号" align="center" prop="phone" />
        <el-table-column label="邮箱" align="center" prop="email" />
        <!-- <el-table-column label="头像" align="center" prop="avatar" /> -->
        <el-table-column label="性别" align="center" prop="gender">
          <template #default="scope">
            <span>{{ scope.row.gender === '1' ? '男' : scope.row.gender === '2' ? '女' : '未知' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="生日" align="center" prop="birthday" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.birthday, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="省份" align="center" prop="province" />
        <el-table-column label="城市" align="center" prop="city" />
        <el-table-column label="区县" align="center" prop="district" />
        <el-table-column label="详细地址" align="center" prop="address" />
        <!-- <el-table-column label="注册渠道" align="center" prop="registerChannel" />
        <el-table-column label="注册IP" align="center" prop="registerIp" /> -->
        <el-table-column label="状态" align="center" prop="status" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['member:info:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['member:info:remove']"></el-button>
            </el-tooltip>
            <el-tooltip content="充值" placement="top">
              <el-button link type="success" @click="handleRecharge(scope.row)">充值</el-button>
            </el-tooltip>
            <el-tooltip content="消费" placement="top">
              <el-button link type="warning" @click="handleConsume(scope.row)">消费</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改会员信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="infoFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="会员编号" prop="memberNo">
          <el-input v-model="form.memberNo" placeholder="请输入会员编号" />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="form.nickname" placeholder="请输入昵称" />
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="form.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱" />
        </el-form-item>
        <!-- <el-form-item label="头像" prop="avatar">
            <el-input v-model="form.avatar" type="textarea" placeholder="请输入内容" />
        </el-form-item> -->
        <el-form-item label="性别" prop="gender">
          <el-select v-model="form.gender" placeholder="请选择性别" clearable>
            <el-option label="未知" value="0" />
            <el-option label="男" value="1" />
            <el-option label="女" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="生日" prop="birthday">
          <el-date-picker clearable v-model="form.birthday" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择生日">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="省份" prop="province">
          <el-input v-model="form.province" placeholder="请输入省份" />
        </el-form-item>
        <el-form-item label="城市" prop="city">
          <el-input v-model="form.city" placeholder="请输入城市" />
        </el-form-item>
        <el-form-item label="区县" prop="district">
          <el-input v-model="form.district" placeholder="请输入区县" />
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入详细地址" />
        </el-form-item>
        <!-- <el-form-item label="登录密码" prop="password">
          <el-input v-model="form.password" placeholder="请输入登录密码" />
        </el-form-item>
        <el-form-item label="密码盐值" prop="salt">
          <el-input v-model="form.salt" placeholder="请输入密码盐值" />
        </el-form-item> -->
        <!-- <el-form-item label="注册渠道" prop="registerChannel">
          <el-input v-model="form.registerChannel" placeholder="请输入注册渠道：APP, WEB, WECHAT, ALIPAY" />
        </el-form-item>
        <el-form-item label="注册IP" prop="registerIp">
          <el-input v-model="form.registerIp" placeholder="请输入注册IP" />
        </el-form-item> -->
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 充值对话框 -->
    <el-dialog title="会员充值" v-model="rechargeDialog.visible" width="400px" append-to-body>
      <el-form ref="rechargeFormRef" :model="rechargeForm" :rules="rechargeRules" label-width="80px">
        <el-form-item label="会员信息">
          <span>{{ rechargeDialog.memberInfo }}</span>
        </el-form-item>
        <el-form-item label="充值金额" prop="amount">
          <el-input-number v-model="rechargeForm.rechargeAmount" :min="0.01" :precision="2" placeholder="请输入充值金额" style="width: 100%" />
        </el-form-item>
        <el-form-item label="支付方式" prop="paymentMethod">
          <el-select v-model="rechargeForm.paymentMethod" placeholder="请选择支付方式" style="width: 100%">
            <el-option label="微信支付" value="WECHAT" />
            <el-option label="支付宝" value="ALIPAY" />
            <el-option label="银行卡" value="BANK_CARD" />
            <el-option label="现金" value="CASH" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="rechargeLoading" type="primary" @click="submitRecharge">确 定</el-button>
          <el-button @click="cancelRecharge">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 消费对话框 -->
    <el-dialog title="会员消费" v-model="consumeDialog.visible" width="400px" append-to-body>
      <el-form ref="consumeFormRef" :model="consumeForm" :rules="consumeRules" label-width="80px">
        <el-form-item label="会员信息">
          <span>{{ consumeDialog.memberInfo }}</span>
        </el-form-item>
        <el-form-item label="消费金额" prop="amount">
          <el-input-number v-model="consumeForm.consumeAmount" :min="0.01" :precision="2" placeholder="请输入消费金额" style="width: 100%" />
        </el-form-item>
        <el-form-item label="订单号" prop="orderNo">
          <el-input v-model="consumeForm.orderNo" placeholder="请输入订单号" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="consumeLoading" type="primary" @click="submitConsume">确 定</el-button>
          <el-button @click="cancelConsume">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Info" lang="ts">
import { listInfo, getInfo, delInfo, addInfo, updateInfo } from '@/api/member/info';
import { InfoVO, InfoQuery, InfoForm } from '@/api/member/info/types';
import { rechargeWallet, consumeWallet } from '@/api/member/walletAccount';
import { parseTime } from '@/utils/ruoyi';
import modal from '@/plugins/modal';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const infoList = ref<InfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const infoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

// 充值对话框
const rechargeDialog = reactive({
  visible: false,
  memberInfo: ''
});

const rechargeForm = reactive({
  memberId: undefined as string | number | undefined,
  rechargeAmount: undefined as number | undefined,
  paymentMethod: undefined as string | undefined
});

const rechargeFormRef = ref<ElFormInstance>();
const rechargeLoading = ref(false);

// 消费对话框
const consumeDialog = reactive({
  visible: false,
  memberInfo: ''
});

const consumeForm = reactive({
  memberId: undefined as string | number | undefined,
  consumeAmount: undefined as number | undefined,
  orderNo: undefined as string | undefined
});

const consumeFormRef = ref<ElFormInstance>();
const consumeLoading = ref(false);

const initFormData: InfoForm = {
  id: undefined,
  memberNo: undefined,
  nickname: undefined,
  realName: undefined,
  phone: undefined,
  email: undefined,
  avatar: undefined,
  gender: undefined,
  birthday: undefined,
  province: undefined,
  city: undefined,
  district: undefined,
  address: undefined,
  password: undefined,
  salt: undefined,
  registerChannel: undefined,
  registerIp: undefined,
  status: undefined,
  remark: undefined
};
const data = reactive<PageData<InfoForm, InfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    memberNo: undefined,
    nickname: undefined,
    realName: undefined,
    phone: undefined,
    email: undefined,
    avatar: undefined,
    gender: undefined,
    birthday: undefined,
    province: undefined,
    city: undefined,
    district: undefined,
    address: undefined,
    password: undefined,
    salt: undefined,
    registerChannel: undefined,
    registerIp: undefined,
    status: undefined,
    params: {}
  },
  rules: {
    nickname: [{ required: true, message: '昵称不能为空', trigger: 'blur' }],
    realName: [{ required: true, message: '真实姓名不能为空', trigger: 'blur' }],
    phone: [{ required: true, message: '手机号不能为空', trigger: 'blur' }],
    email: [{ required: true, message: '邮箱不能为空', trigger: 'blur' }],
    gender: [{ required: true, message: '性别不能为空', trigger: 'blur' }],
    birthday: [{ required: true, message: '生日不能为空', trigger: 'blur' }],
    province: [{ required: true, message: '省份不能为空', trigger: 'blur' }],
    city: [{ required: true, message: '城市不能为空', trigger: 'blur' }],
    district: [{ required: true, message: '区县不能为空', trigger: 'blur' }],
    address: [{ required: true, message: '详细地址不能为空', trigger: 'blur' }],
    remark: [{ required: true, message: '备注不能为空', trigger: 'blur' }]
  }
});

// 充值表单验证规则
const rechargeRules = reactive({
  rechargeAmount: [
    { required: true, message: '充值金额不能为空', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '充值金额必须大于0', trigger: 'blur' }
  ],
  paymentMethod: [{ required: true, message: '支付方式不能为空', trigger: 'change' }]
});

// 消费表单验证规则
const consumeRules = reactive({
  consumeAmount: [
    { required: true, message: '消费金额不能为空', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '消费金额必须大于0', trigger: 'blur' }
  ],
  orderNo: [{ required: true, message: '订单号不能为空', trigger: 'blur' }]
});

const { queryParams, form, rules } = toRefs(data);

/** 查询会员信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listInfo(queryParams.value);
  infoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  infoFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: InfoVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加会员信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: InfoVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getInfo(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改会员信息';
};

/** 提交按钮 */
const submitForm = () => {
  infoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateInfo(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addInfo(form.value).finally(() => (buttonLoading.value = false));
      }
      modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: InfoVO) => {
  const _ids = row?.id || ids.value;
  await modal.confirm('是否确认删除会员信息编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delInfo(_ids);
  modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'member/info/export',
    {
      ...queryParams.value
    },
    `info_${new Date().getTime()}.xlsx`
  );
};

/** 充值按钮操作 */
const handleRecharge = (row: InfoVO) => {
  rechargeForm.memberId = row.id;
  rechargeForm.amount = undefined;
  rechargeForm.paymentMethod = undefined;
  rechargeDialog.memberInfo = `${row.nickname || row.memberNo} (${row.realName || '未填写'})`;
  rechargeDialog.visible = true;
};

/** 消费按钮操作 */
const handleConsume = (row: InfoVO) => {
  consumeForm.memberId = row.id;
  consumeForm.amount = undefined;
  consumeForm.orderNo = undefined;
  consumeDialog.memberInfo = `${row.nickname || row.memberNo} (${row.realName || '未填写'})`;
  consumeDialog.visible = true;
};

/** 取消充值 */
const cancelRecharge = () => {
  rechargeDialog.visible = false;
  rechargeFormRef.value?.resetFields();
};

/** 取消消费 */
const cancelConsume = () => {
  consumeDialog.visible = false;
  consumeFormRef.value?.resetFields();
};

/** 提交充值 */
const submitRecharge = () => {
  rechargeFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      rechargeLoading.value = true;
      try {
        await rechargeWallet({
          memberId: rechargeForm.memberId!,
          rechargeAmount: rechargeForm.rechargeAmount!,
          paymentMethod: rechargeForm.paymentMethod!
        });
        modal.msgSuccess('充值成功');
        rechargeDialog.visible = false;
        rechargeFormRef.value?.resetFields();
      } catch (error) {
        console.error('充值失败:', error);
      } finally {
        rechargeLoading.value = false;
      }
    }
  });
};

/** 提交消费 */
const submitConsume = () => {
  consumeFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      consumeLoading.value = true;
      try {
        await consumeWallet({
          memberId: consumeForm.memberId!,
          consumeAmount: consumeForm.consumeAmount!,
          orderNo: consumeForm.orderNo!
        });
        modal.msgSuccess('消费成功');
        consumeDialog.visible = false;
        consumeFormRef.value?.resetFields();
      } catch (error) {
        console.error('消费失败:', error);
      } finally {
        consumeLoading.value = false;
      }
    }
  });
};

onMounted(() => {
  getList();
});
</script>
