export interface UsageRecordVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 会员ID
   */
  memberId: string | number;

  /**
   * 订单号
   */
  orderNo: string;

  /**
   * 使用的规则ID
   */
  ruleId: string | number;

  /**
   * 规则名称（冗余字段）
   */
  ruleName: string;

  /**
   * 规则类型
   */
  ruleType: string;

  /**
   * 原始金额
   */
  originalAmount: number;

  /**
   * 优惠金额
   */
  discountAmount: number;

  /**
   * 最终金额
   */
  finalAmount: number;

  /**
   * 适用商品信息（JSON格式）
   */
  applicableProducts: string;

  /**
   * 使用时间
   */
  usageTime: string;

  /**
   * 状态：1-已使用，2-已退款
   */
  status: string;

  /**
   * 备注说明
   */
  remark: string;
}

export interface UsageRecordForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 会员ID
   */
  memberId?: string | number;

  /**
   * 订单号
   */
  orderNo?: string;

  /**
   * 使用的规则ID
   */
  ruleId?: string | number;

  /**
   * 规则名称（冗余字段）
   */
  ruleName?: string;

  /**
   * 规则类型
   */
  ruleType?: string;

  /**
   * 原始金额
   */
  originalAmount?: number;

  /**
   * 优惠金额
   */
  discountAmount?: number;

  /**
   * 最终金额
   */
  finalAmount?: number;

  /**
   * 适用商品信息（JSON格式）
   */
  applicableProducts?: string;

  /**
   * 使用时间
   */
  usageTime?: string;

  /**
   * 状态：1-已使用，2-已退款
   */
  status?: string;

  /**
   * 备注说明
   */
  remark?: string;
}

export interface UsageRecordQuery extends PageQuery {
  /**
   * 会员ID
   */
  memberId?: string | number;

  /**
   * 订单号
   */
  orderNo?: string;

  /**
   * 使用的规则ID
   */
  ruleId?: string | number;

  /**
   * 规则名称（冗余字段）
   */
  ruleName?: string;

  /**
   * 规则类型
   */
  ruleType?: string;

  /**
   * 原始金额
   */
  originalAmount?: number;

  /**
   * 优惠金额
   */
  discountAmount?: number;

  /**
   * 最终金额
   */
  finalAmount?: number;

  /**
   * 适用商品信息（JSON格式）
   */
  applicableProducts?: string;

  /**
   * 使用时间
   */
  usageTime?: string;

  /**
   * 状态：1-已使用，2-已退款
   */
  status?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
