import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { WalletDetailVO, WalletDetailForm, WalletDetailQuery } from '@/api/member/walletDetail/types';

/**
 * 查询钱包流水记录列表
 * @param query
 * @returns {*}
 */

export const listWalletDetail = (query?: WalletDetailQuery): AxiosPromise<WalletDetailVO[]> => {
  return request({
    url: '/member/walletDetail/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询钱包流水记录详细
 * @param id
 */
export const getWalletDetail = (id: string | number): AxiosPromise<WalletDetailVO> => {
  return request({
    url: '/member/walletDetail/' + id,
    method: 'get'
  });
};

/**
 * 新增钱包流水记录
 * @param data
 */
export const addWalletDetail = (data: WalletDetailForm) => {
  return request({
    url: '/member/walletDetail',
    method: 'post',
    data: data
  });
};

/**
 * 修改钱包流水记录
 * @param data
 */
export const updateWalletDetail = (data: WalletDetailForm) => {
  return request({
    url: '/member/walletDetail',
    method: 'put',
    data: data
  });
};

/**
 * 删除钱包流水记录
 * @param id
 */
export const delWalletDetail = (id: string | number | Array<string | number>) => {
  return request({
    url: '/member/walletDetail/' + id,
    method: 'delete'
  });
};
