export interface LadderVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 关联规则ID
   */
  ruleId: string | number;

  /**
   * 最小金额
   */
  minAmount: number;

  /**
   * 最大金额
   */
  maxAmount: number;

  /**
   * 优惠值
   */
  discountValue: number;

  /**
   * 排序
   */
  sortOrder: number;
}

export interface LadderForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 关联规则ID
   */
  ruleId?: string | number;

  /**
   * 最小金额
   */
  minAmount?: number;

  /**
   * 最大金额
   */
  maxAmount?: number;

  /**
   * 优惠值
   */
  discountValue?: number;

  /**
   * 排序
   */
  sortOrder?: number;
}

export interface LadderQuery extends PageQuery {
  /**
   * 关联规则ID
   */
  ruleId?: string | number;

  /**
   * 最小金额
   */
  minAmount?: number;

  /**
   * 最大金额
   */
  maxAmount?: number;

  /**
   * 优惠值
   */
  discountValue?: number;

  /**
   * 排序
   */
  sortOrder?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
