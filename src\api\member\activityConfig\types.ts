export interface ActivityConfigVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 活动名称
   */
  activityName: string;

  /**
   * 充值金额
   */
  rechargeAmount: number;

  /**
   * 赠送金额
   */
  bonusAmount: number;

  /**
   * 活动开始时间
   */
  startTime: string;

  /**
   * 活动结束时间
   */
  endTime: string;

  /**
   * 状态：0-启用，1-禁用
   */
  status: string;

  /**
   * 排序（数字越大优先级越高）
   */
  sortOrder: number;

  /**
   * 备注说明
   */
  remark: string;
}

export interface ActivityConfigForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 活动名称
   */
  activityName?: string;

  /**
   * 充值金额
   */
  rechargeAmount?: number;

  /**
   * 赠送金额
   */
  bonusAmount?: number;

  /**
   * 活动开始时间
   */
  startTime?: string;

  /**
   * 活动结束时间
   */
  endTime?: string;

  /**
   * 状态：0-启用，1-禁用
   */
  status?: string;

  /**
   * 排序（数字越大优先级越高）
   */
  sortOrder?: number;

  /**
   * 备注说明
   */
  remark?: string;
}

export interface ActivityConfigQuery extends PageQuery {
  /**
   * 活动名称
   */
  activityName?: string;

  /**
   * 充值金额
   */
  rechargeAmount?: number;

  /**
   * 赠送金额
   */
  bonusAmount?: number;

  /**
   * 活动开始时间
   */
  startTime?: string;

  /**
   * 活动结束时间
   */
  endTime?: string;

  /**
   * 状态：0-启用，1-禁用
   */
  status?: string;

  /**
   * 排序（数字越大优先级越高）
   */
  sortOrder?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
