export interface StatisticsVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 会员ID
   */
  memberId: string | number;

  /**
   * 累计消费金额
   */
  totalConsumeAmount: number;

  /**
   * 累计消费次数
   */
  totalConsumeCount: number;

  /**
   * 累计充值金额
   */
  totalRechargeAmount: number;

  /**
   * 累计充值次数
   */
  totalRechargeCount: number;

  /**
   * 最后消费时间
   */
  lastConsumeTime: string;

  /**
   * 最后充值时间
   */
  lastRechargeTime: string;
}

export interface StatisticsForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 会员ID
   */
  memberId?: string | number;

  /**
   * 累计消费金额
   */
  totalConsumeAmount?: number;

  /**
   * 累计消费次数
   */
  totalConsumeCount?: number;

  /**
   * 累计充值金额
   */
  totalRechargeAmount?: number;

  /**
   * 累计充值次数
   */
  totalRechargeCount?: number;

  /**
   * 最后消费时间
   */
  lastConsumeTime?: string;

  /**
   * 最后充值时间
   */
  lastRechargeTime?: string;
}

export interface StatisticsQuery extends PageQuery {
  /**
   * 会员ID
   */
  memberId?: string | number;

  /**
   * 累计消费金额
   */
  totalConsumeAmount?: number;

  /**
   * 累计消费次数
   */
  totalConsumeCount?: number;

  /**
   * 累计充值金额
   */
  totalRechargeAmount?: number;

  /**
   * 累计充值次数
   */
  totalRechargeCount?: number;

  /**
   * 最后消费时间
   */
  lastConsumeTime?: string;

  /**
   * 最后充值时间
   */
  lastRechargeTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
