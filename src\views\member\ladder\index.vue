<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="关联规则ID" prop="ruleId">
              <el-input v-model="queryParams.ruleId" placeholder="请输入关联规则ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="最小金额" prop="minAmount">
              <el-input v-model="queryParams.minAmount" placeholder="请输入最小金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="最大金额" prop="maxAmount">
              <el-input v-model="queryParams.maxAmount" placeholder="请输入最大金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="优惠值" prop="discountValue">
              <el-input v-model="queryParams.discountValue" placeholder="请输入优惠值" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="排序" prop="sortOrder">
              <el-input v-model="queryParams.sortOrder" placeholder="请输入排序" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['member:ladder:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['member:ladder:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['member:ladder:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['member:ladder:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="ladderList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键ID" align="center" prop="id" v-if="true" />
        <el-table-column label="关联规则ID" align="center" prop="ruleId" />
        <el-table-column label="最小金额" align="center" prop="minAmount" />
        <el-table-column label="最大金额" align="center" prop="maxAmount" />
        <el-table-column label="优惠值" align="center" prop="discountValue" />
        <el-table-column label="排序" align="center" prop="sortOrder" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['member:ladder:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['member:ladder:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改阶梯优惠配置对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="ladderFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="关联规则ID" prop="ruleId">
          <el-input v-model="form.ruleId" placeholder="请输入关联规则ID" />
        </el-form-item>
        <el-form-item label="最小金额" prop="minAmount">
          <el-input v-model="form.minAmount" placeholder="请输入最小金额" />
        </el-form-item>
        <el-form-item label="最大金额" prop="maxAmount">
          <el-input v-model="form.maxAmount" placeholder="请输入最大金额" />
        </el-form-item>
        <el-form-item label="优惠值" prop="discountValue">
          <el-input v-model="form.discountValue" placeholder="请输入优惠值" />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input v-model="form.sortOrder" placeholder="请输入排序" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Ladder" lang="ts">
import { listLadder, getLadder, delLadder, addLadder, updateLadder } from '@/api/member/ladder';
import { LadderVO, LadderQuery, LadderForm } from '@/api/member/ladder/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const ladderList = ref<LadderVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const ladderFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: LadderForm = {
  id: undefined,
  ruleId: undefined,
  minAmount: undefined,
  maxAmount: undefined,
  discountValue: undefined,
  sortOrder: undefined
};
const data = reactive<PageData<LadderForm, LadderQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    ruleId: undefined,
    minAmount: undefined,
    maxAmount: undefined,
    discountValue: undefined,
    sortOrder: undefined,
    params: {}
  },
  rules: {
    maxAmount: [{ required: true, message: '最大金额不能为空', trigger: 'blur' }],
    sortOrder: [{ required: true, message: '排序不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询阶梯优惠配置列表 */
const getList = async () => {
  loading.value = true;
  const res = await listLadder(queryParams.value);
  ladderList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  ladderFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: LadderVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加阶梯优惠配置';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: LadderVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getLadder(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改阶梯优惠配置';
};

/** 提交按钮 */
const submitForm = () => {
  ladderFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateLadder(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addLadder(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: LadderVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除阶梯优惠配置编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delLadder(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'member/ladder/export',
    {
      ...queryParams.value
    },
    `ladder_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
