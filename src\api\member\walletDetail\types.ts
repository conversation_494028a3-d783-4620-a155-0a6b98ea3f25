export interface WalletDetailVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 会员ID
   */
  memberId: string | number;

  /**
   * 会员名称
   */
  memberName: string;

  /**
   * 关联订单号
   */
  orderNo: string;

  /**
   * 业务类型：1-充值，2-消费，3-退款，4-转账，5-活动赠送，6-管理员调整
   */
  businessType: string;

  /**
   * 金额类型：1-充值金额，2-赠送金额
   */
  amountType: string;

  /**
   * 变动金额（正数为增加，负数为减少）
   */
  changeAmount: number;

  /**
   * 变动前余额
   */
  balanceBefore: number;

  /**
   * 变动后余额
   */
  balanceAfter: number;

  /**
   * 变动前充值余额
   */
  rechargeBalanceBefore: number;

  /**
   * 变动后充值余额
   */
  rechargeBalanceAfter: number;

  /**
   * 变动前赠送余额
   */
  bonusBalanceBefore: number;

  /**
   * 变动后赠送余额
   */
  bonusBalanceAfter: number;

  /**
   * 关联活动ID
   */
  activityId: string | number;

  /**
   * 备注说明
   */
  remark: string;
}

export interface WalletDetailForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 会员ID
   */
  memberId?: string | number;

  /**
   * 会员名称
   */
  memberName?: string;

  /**
   * 关联订单号
   */
  orderNo?: string;

  /**
   * 业务类型：1-充值，2-消费，3-退款，4-转账，5-活动赠送，6-管理员调整
   */
  businessType?: string;

  /**
   * 金额类型：1-充值金额，2-赠送金额
   */
  amountType?: string;

  /**
   * 变动金额（正数为增加，负数为减少）
   */
  changeAmount?: number;

  /**
   * 变动前余额
   */
  balanceBefore?: number;

  /**
   * 变动后余额
   */
  balanceAfter?: number;

  /**
   * 变动前充值余额
   */
  rechargeBalanceBefore?: number;

  /**
   * 变动后充值余额
   */
  rechargeBalanceAfter?: number;

  /**
   * 变动前赠送余额
   */
  bonusBalanceBefore?: number;

  /**
   * 变动后赠送余额
   */
  bonusBalanceAfter?: number;

  /**
   * 关联活动ID
   */
  activityId?: string | number;

  /**
   * 备注说明
   */
  remark?: string;
}

export interface WalletDetailQuery extends PageQuery {
  /**
   * 会员ID
   */
  memberId?: string | number;

  /**
   * 会员名称
   */
  memberName?: string;

  /**
   * 关联订单号
   */
  orderNo?: string;

  /**
   * 业务类型：1-充值，2-消费，3-退款，4-转账，5-活动赠送，6-管理员调整
   */
  businessType?: string;

  /**
   * 金额类型：1-充值金额，2-赠送金额
   */
  amountType?: string;

  /**
   * 变动金额（正数为增加，负数为减少）
   */
  changeAmount?: number;

  /**
   * 变动前余额
   */
  balanceBefore?: number;

  /**
   * 变动后余额
   */
  balanceAfter?: number;

  /**
   * 变动前充值余额
   */
  rechargeBalanceBefore?: number;

  /**
   * 变动后充值余额
   */
  rechargeBalanceAfter?: number;

  /**
   * 变动前赠送余额
   */
  bonusBalanceBefore?: number;

  /**
   * 变动后赠送余额
   */
  bonusBalanceAfter?: number;

  /**
   * 关联活动ID
   */
  activityId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}
