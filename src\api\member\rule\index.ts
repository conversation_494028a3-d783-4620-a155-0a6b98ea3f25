import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { RuleVO, RuleForm, RuleQuery } from '@/api/member/rule/types';

/**
 * 查询优惠规则配置列表
 * @param query
 * @returns {*}
 */

export const listRule = (query?: RuleQuery): AxiosPromise<RuleVO[]> => {
  return request({
    url: '/member/rule/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询优惠规则配置详细
 * @param id
 */
export const getRule = (id: string | number): AxiosPromise<RuleVO> => {
  return request({
    url: '/member/rule/' + id,
    method: 'get'
  });
};

/**
 * 新增优惠规则配置
 * @param data
 */
export const addRule = (data: RuleForm) => {
  return request({
    url: '/member/rule',
    method: 'post',
    data: data
  });
};

/**
 * 修改优惠规则配置
 * @param data
 */
export const updateRule = (data: RuleForm) => {
  return request({
    url: '/member/rule',
    method: 'put',
    data: data
  });
};

/**
 * 删除优惠规则配置
 * @param id
 */
export const delRule = (id: string | number | Array<string | number>) => {
  return request({
    url: '/member/rule/' + id,
    method: 'delete'
  });
};
