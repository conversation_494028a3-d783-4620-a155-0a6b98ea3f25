export interface DiscountStatsVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 会员ID
   */
  memberId: string | number;

  /**
   * 规则ID
   */
  ruleId: string | number;

  /**
   * 已使用次数
   */
  usedCount: number;

  /**
   * 最后使用时间
   */
  lastUsedTime: string;
}

export interface DiscountStatsForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 会员ID
   */
  memberId?: string | number;

  /**
   * 规则ID
   */
  ruleId?: string | number;

  /**
   * 已使用次数
   */
  usedCount?: number;

  /**
   * 最后使用时间
   */
  lastUsedTime?: string;
}

export interface DiscountStatsQuery extends PageQuery {
  /**
   * 会员ID
   */
  memberId?: string | number;

  /**
   * 规则ID
   */
  ruleId?: string | number;

  /**
   * 已使用次数
   */
  usedCount?: number;

  /**
   * 最后使用时间
   */
  lastUsedTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
