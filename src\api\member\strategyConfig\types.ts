export interface StrategyConfigVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 策略名称
   */
  strategyName: string;

  /**
   * 策略类型：1-优先扣除充值金额，2-优先扣除赠送金额，3-按比例扣除
   */
  strategyType: string;

  /**
   * 充值金额扣除比例（0-100）
   */
  rechargeRatio: number;

  /**
   * 赠送金额扣除比例（0-100）
   */
  bonusRatio: number;

  /**
   * 状态：0-启用，1-禁用
   */
  status: string;

  /**
   * 是否默认策略：0-否，1-是
   */
  isDefault: string;

  /**
   * 备注说明
   */
  remark: string;
}

export interface StrategyConfigForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 策略名称
   */
  strategyName?: string;

  /**
   * 策略类型：1-优先扣除充值金额，2-优先扣除赠送金额，3-按比例扣除
   */
  strategyType?: string;

  /**
   * 充值金额扣除比例（0-100）
   */
  rechargeRatio?: number;

  /**
   * 赠送金额扣除比例（0-100）
   */
  bonusRatio?: number;

  /**
   * 状态：0-启用，1-禁用
   */
  status?: string;

  /**
   * 是否默认策略：0-否，1-是
   */
  isDefault?: string;

  /**
   * 备注说明
   */
  remark?: string;
}

export interface StrategyConfigQuery extends PageQuery {
  /**
   * 策略名称
   */
  strategyName?: string;

  /**
   * 策略类型：1-优先扣除充值金额，2-优先扣除赠送金额，3-按比例扣除
   */
  strategyType?: string;

  /**
   * 充值金额扣除比例（0-100）
   */
  rechargeRatio?: number;

  /**
   * 赠送金额扣除比例（0-100）
   */
  bonusRatio?: number;

  /**
   * 状态：0-启用，1-禁用
   */
  status?: string;

  /**
   * 是否默认策略：0-否，1-是
   */
  isDefault?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
