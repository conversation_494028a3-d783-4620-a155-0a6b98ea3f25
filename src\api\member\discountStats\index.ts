import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DiscountStatsVO, DiscountStatsForm, DiscountStatsQuery } from '@/api/member/discountStats/types';

/**
 * 查询会员优惠使用统计列表
 * @param query
 * @returns {*}
 */

export const listDiscountStats = (query?: DiscountStatsQuery): AxiosPromise<DiscountStatsVO[]> => {
  return request({
    url: '/member/discountStats/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询会员优惠使用统计详细
 * @param id
 */
export const getDiscountStats = (id: string | number): AxiosPromise<DiscountStatsVO> => {
  return request({
    url: '/member/discountStats/' + id,
    method: 'get'
  });
};

/**
 * 新增会员优惠使用统计
 * @param data
 */
export const addDiscountStats = (data: DiscountStatsForm) => {
  return request({
    url: '/member/discountStats',
    method: 'post',
    data: data
  });
};

/**
 * 修改会员优惠使用统计
 * @param data
 */
export const updateDiscountStats = (data: DiscountStatsForm) => {
  return request({
    url: '/member/discountStats',
    method: 'put',
    data: data
  });
};

/**
 * 删除会员优惠使用统计
 * @param id
 */
export const delDiscountStats = (id: string | number | Array<string | number>) => {
  return request({
    url: '/member/discountStats/' + id,
    method: 'delete'
  });
};
