export interface RuleVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 规则名称
   */
  ruleName: string;

  /**
   * 规则类型：1-满减，2-折扣，3-买赠，4-阶梯满减
   */
  ruleType: string;

  /**
   * 最小消费金额
   */
  minAmount: number;

  /**
   * 最大消费金额（null表示无上限）
   */
  maxAmount: number;

  /**
   * 优惠值（满减金额或折扣比例）
   */
  discountValue: number;

  /**
   * 最大优惠金额（折扣时使用）
   */
  maxDiscountAmount: number;

  /**
   * 适用商品（JSON格式，null表示全商品）
   */
  applicableProducts: string;

  /**
   * 排除商品（JSON格式）
   */
  excludeProducts: string;

  /**
   * 用户限制类型：0-无限制，1-每人限用，2-新用户专享
   */
  userLimitType: string;

  /**
   * 每人限用次数
   */
  userLimitCount: number;

  /**
   * 总使用次数限制
   */
  totalLimitCount: number;

  /**
   * 已使用次数
   */
  usedCount: number;

  /**
   * 规则生效开始时间
   */
  startTime: string;

  /**
   * 规则生效结束时间
   */
  endTime: string;

  /**
   * 状态：0-启用，1-禁用
   */
  status: string;

  /**
   * 优先级（数字越大优先级越高）
   */
  priority: number;

  /**
   * 是否可叠加：0-不可叠加，1-可叠加
   */
  canStack: string;

  /**
   * 备注说明
   */
  remark: string;
}

export interface RuleForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 规则名称
   */
  ruleName?: string;

  /**
   * 规则类型：1-满减，2-折扣，3-买赠，4-阶梯满减
   */
  ruleType?: string;

  /**
   * 最小消费金额
   */
  minAmount?: number;

  /**
   * 最大消费金额（null表示无上限）
   */
  maxAmount?: number;

  /**
   * 优惠值（满减金额或折扣比例）
   */
  discountValue?: number;

  /**
   * 最大优惠金额（折扣时使用）
   */
  maxDiscountAmount?: number;

  /**
   * 适用商品（JSON格式，null表示全商品）
   */
  applicableProducts?: string;

  /**
   * 排除商品（JSON格式）
   */
  excludeProducts?: string;

  /**
   * 用户限制类型：0-无限制，1-每人限用，2-新用户专享
   */
  userLimitType?: string;

  /**
   * 每人限用次数
   */
  userLimitCount?: number;

  /**
   * 总使用次数限制
   */
  totalLimitCount?: number;

  /**
   * 已使用次数
   */
  usedCount?: number;

  /**
   * 规则生效开始时间
   */
  startTime?: string;

  /**
   * 规则生效结束时间
   */
  endTime?: string;

  /**
   * 状态：0-启用，1-禁用
   */
  status?: string;

  /**
   * 优先级（数字越大优先级越高）
   */
  priority?: number;

  /**
   * 是否可叠加：0-不可叠加，1-可叠加
   */
  canStack?: string;

  /**
   * 备注说明
   */
  remark?: string;
}

export interface RuleQuery extends PageQuery {
  /**
   * 规则名称
   */
  ruleName?: string;

  /**
   * 规则类型：1-满减，2-折扣，3-买赠，4-阶梯满减
   */
  ruleType?: string;

  /**
   * 最小消费金额
   */
  minAmount?: number;

  /**
   * 最大消费金额（null表示无上限）
   */
  maxAmount?: number;

  /**
   * 优惠值（满减金额或折扣比例）
   */
  discountValue?: number;

  /**
   * 最大优惠金额（折扣时使用）
   */
  maxDiscountAmount?: number;

  /**
   * 适用商品（JSON格式，null表示全商品）
   */
  applicableProducts?: string;

  /**
   * 排除商品（JSON格式）
   */
  excludeProducts?: string;

  /**
   * 用户限制类型：0-无限制，1-每人限用，2-新用户专享
   */
  userLimitType?: string;

  /**
   * 每人限用次数
   */
  userLimitCount?: number;

  /**
   * 总使用次数限制
   */
  totalLimitCount?: number;

  /**
   * 已使用次数
   */
  usedCount?: number;

  /**
   * 规则生效开始时间
   */
  startTime?: string;

  /**
   * 规则生效结束时间
   */
  endTime?: string;

  /**
   * 状态：0-启用，1-禁用
   */
  status?: string;

  /**
   * 优先级（数字越大优先级越高）
   */
  priority?: number;

  /**
   * 是否可叠加：0-不可叠加，1-可叠加
   */
  canStack?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
