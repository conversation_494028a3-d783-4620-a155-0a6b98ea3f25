<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="策略名称" prop="strategyName">
              <el-input v-model="queryParams.strategyName" placeholder="请输入策略名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="充值金额扣除比例" prop="rechargeRatio">
              <el-input v-model="queryParams.rechargeRatio" placeholder="请输入充值金额扣除比例" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="赠送金额扣除比例" prop="bonusRatio">
              <el-input v-model="queryParams.bonusRatio" placeholder="请输入赠送金额扣除比例" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否默认策略：0-否，1-是" prop="isDefault">
              <el-input v-model="queryParams.isDefault" placeholder="请输入是否默认策略：0-否，1-是" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['member:strategyConfig:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['member:strategyConfig:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['member:strategyConfig:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['member:strategyConfig:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="strategyConfigList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键ID" align="center" prop="id" v-if="true" />
        <el-table-column label="策略名称" align="center" prop="strategyName" />
        <el-table-column label="策略类型：1-优先扣除充值金额，2-优先扣除赠送金额，3-按比例扣除" align="center" prop="strategyType" />
        <el-table-column label="充值金额扣除比例" align="center" prop="rechargeRatio" />
        <el-table-column label="赠送金额扣除比例" align="center" prop="bonusRatio" />
        <el-table-column label="状态：0-启用，1-禁用" align="center" prop="status" />
        <el-table-column label="是否默认策略：0-否，1-是" align="center" prop="isDefault" />
        <el-table-column label="备注说明" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['member:strategyConfig:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['member:strategyConfig:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改消费策略配置对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="strategyConfigFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="策略名称" prop="strategyName">
          <el-input v-model="form.strategyName" placeholder="请输入策略名称" />
        </el-form-item>
        <el-form-item label="充值金额扣除比例" prop="rechargeRatio">
          <el-input v-model="form.rechargeRatio" placeholder="请输入充值金额扣除比例" />
        </el-form-item>
        <el-form-item label="赠送金额扣除比例" prop="bonusRatio">
          <el-input v-model="form.bonusRatio" placeholder="请输入赠送金额扣除比例" />
        </el-form-item>
        <el-form-item label="是否默认策略：0-否，1-是" prop="isDefault">
          <el-input v-model="form.isDefault" placeholder="请输入是否默认策略：0-否，1-是" />
        </el-form-item>
        <el-form-item label="备注说明" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="StrategyConfig" lang="ts">
import { listStrategyConfig, getStrategyConfig, delStrategyConfig, addStrategyConfig, updateStrategyConfig } from '@/api/member/strategyConfig';
import { StrategyConfigVO, StrategyConfigQuery, StrategyConfigForm } from '@/api/member/strategyConfig/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const strategyConfigList = ref<StrategyConfigVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const strategyConfigFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: StrategyConfigForm = {
  id: undefined,
  strategyName: undefined,
  strategyType: undefined,
  rechargeRatio: undefined,
  bonusRatio: undefined,
  status: undefined,
  isDefault: undefined,
  remark: undefined
};
const data = reactive<PageData<StrategyConfigForm, StrategyConfigQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    strategyName: undefined,
    strategyType: undefined,
    rechargeRatio: undefined,
    bonusRatio: undefined,
    status: undefined,
    isDefault: undefined,
    params: {}
  },
  rules: {
    rechargeRatio: [{ required: true, message: '充值金额扣除比例不能为空', trigger: 'blur' }],
    bonusRatio: [{ required: true, message: '赠送金额扣除比例不能为空', trigger: 'blur' }],
    remark: [{ required: true, message: '备注说明不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询消费策略配置列表 */
const getList = async () => {
  loading.value = true;
  const res = await listStrategyConfig(queryParams.value);
  strategyConfigList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  strategyConfigFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: StrategyConfigVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加消费策略配置';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: StrategyConfigVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getStrategyConfig(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改消费策略配置';
};

/** 提交按钮 */
const submitForm = () => {
  strategyConfigFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateStrategyConfig(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addStrategyConfig(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: StrategyConfigVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除消费策略配置编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delStrategyConfig(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'member/strategyConfig/export',
    {
      ...queryParams.value
    },
    `strategyConfig_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
