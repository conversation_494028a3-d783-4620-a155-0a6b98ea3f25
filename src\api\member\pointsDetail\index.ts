import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PointsDetailVO, PointsDetailForm, PointsDetailQuery } from '@/api/member/pointsDetail/types';

/**
 * 查询积分明细记录列表
 * @param query
 * @returns {*}
 */

export const listPointsDetail = (query?: PointsDetailQuery): AxiosPromise<PointsDetailVO[]> => {
  return request({
    url: '/member/pointsDetail/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询积分明细记录详细
 * @param id
 */
export const getPointsDetail = (id: string | number): AxiosPromise<PointsDetailVO> => {
  return request({
    url: '/member/pointsDetail/' + id,
    method: 'get'
  });
};

/**
 * 新增积分明细记录
 * @param data
 */
export const addPointsDetail = (data: PointsDetailForm) => {
  return request({
    url: '/member/pointsDetail',
    method: 'post',
    data: data
  });
};

/**
 * 修改积分明细记录
 * @param data
 */
export const updatePointsDetail = (data: PointsDetailForm) => {
  return request({
    url: '/member/pointsDetail',
    method: 'put',
    data: data
  });
};

/**
 * 删除积分明细记录
 * @param id
 */
export const delPointsDetail = (id: string | number | Array<string | number>) => {
  return request({
    url: '/member/pointsDetail/' + id,
    method: 'delete'
  });
};
