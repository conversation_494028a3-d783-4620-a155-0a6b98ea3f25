import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { LadderVO, LadderForm, LadderQuery } from '@/api/member/ladder/types';

/**
 * 查询阶梯优惠配置列表
 * @param query
 * @returns {*}
 */

export const listLadder = (query?: LadderQuery): AxiosPromise<LadderVO[]> => {
  return request({
    url: '/member/ladder/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询阶梯优惠配置详细
 * @param id
 */
export const getLadder = (id: string | number): AxiosPromise<LadderVO> => {
  return request({
    url: '/member/ladder/' + id,
    method: 'get'
  });
};

/**
 * 新增阶梯优惠配置
 * @param data
 */
export const addLadder = (data: LadderForm) => {
  return request({
    url: '/member/ladder',
    method: 'post',
    data: data
  });
};

/**
 * 修改阶梯优惠配置
 * @param data
 */
export const updateLadder = (data: LadderForm) => {
  return request({
    url: '/member/ladder',
    method: 'put',
    data: data
  });
};

/**
 * 删除阶梯优惠配置
 * @param id
 */
export const delLadder = (id: string | number | Array<string | number>) => {
  return request({
    url: '/member/ladder/' + id,
    method: 'delete'
  });
};
