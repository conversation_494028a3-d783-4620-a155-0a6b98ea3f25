<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="会员ID" prop="memberId">
              <el-input v-model="queryParams.memberId" placeholder="请输入会员ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="订单号" prop="orderNo">
              <el-input v-model="queryParams.orderNo" placeholder="请输入订单号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="使用的规则ID" prop="ruleId">
              <el-input v-model="queryParams.ruleId" placeholder="请输入使用的规则ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="规则名称" prop="ruleName">
              <el-input v-model="queryParams.ruleName" placeholder="请输入规则名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="原始金额" prop="originalAmount">
              <el-input v-model="queryParams.originalAmount" placeholder="请输入原始金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="优惠金额" prop="discountAmount">
              <el-input v-model="queryParams.discountAmount" placeholder="请输入优惠金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="最终金额" prop="finalAmount">
              <el-input v-model="queryParams.finalAmount" placeholder="请输入最终金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="适用商品信息" prop="applicableProducts">
              <el-input v-model="queryParams.applicableProducts" placeholder="请输入适用商品信息" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="使用时间" prop="usageTime">
              <el-date-picker clearable v-model="queryParams.usageTime" type="date" value-format="YYYY-MM-DD" placeholder="请选择使用时间" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['member:usageRecord:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['member:usageRecord:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['member:usageRecord:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['member:usageRecord:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="usageRecordList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键ID" align="center" prop="id" v-if="true" />
        <el-table-column label="会员ID" align="center" prop="memberId" />
        <el-table-column label="订单号" align="center" prop="orderNo" />
        <el-table-column label="使用的规则ID" align="center" prop="ruleId" />
        <el-table-column label="规则名称" align="center" prop="ruleName" />
        <el-table-column label="规则类型" align="center" prop="ruleType" />
        <el-table-column label="原始金额" align="center" prop="originalAmount" />
        <el-table-column label="优惠金额" align="center" prop="discountAmount" />
        <el-table-column label="最终金额" align="center" prop="finalAmount" />
        <el-table-column label="适用商品信息" align="center" prop="applicableProducts" />
        <el-table-column label="使用时间" align="center" prop="usageTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.usageTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态：1-已使用，2-已退款" align="center" prop="status" />
        <el-table-column label="备注说明" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['member:usageRecord:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['member:usageRecord:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改优惠使用记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="usageRecordFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="会员ID" prop="memberId">
          <el-input v-model="form.memberId" placeholder="请输入会员ID" />
        </el-form-item>
        <el-form-item label="订单号" prop="orderNo">
          <el-input v-model="form.orderNo" placeholder="请输入订单号" />
        </el-form-item>
        <el-form-item label="使用的规则ID" prop="ruleId">
          <el-input v-model="form.ruleId" placeholder="请输入使用的规则ID" />
        </el-form-item>
        <el-form-item label="规则名称" prop="ruleName">
          <el-input v-model="form.ruleName" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="原始金额" prop="originalAmount">
          <el-input v-model="form.originalAmount" placeholder="请输入原始金额" />
        </el-form-item>
        <el-form-item label="优惠金额" prop="discountAmount">
          <el-input v-model="form.discountAmount" placeholder="请输入优惠金额" />
        </el-form-item>
        <el-form-item label="最终金额" prop="finalAmount">
          <el-input v-model="form.finalAmount" placeholder="请输入最终金额" />
        </el-form-item>
        <el-form-item label="适用商品信息" prop="applicableProducts">
          <el-input v-model="form.applicableProducts" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="使用时间" prop="usageTime">
          <el-date-picker clearable v-model="form.usageTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择使用时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注说明" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="UsageRecord" lang="ts">
import { listUsageRecord, getUsageRecord, delUsageRecord, addUsageRecord, updateUsageRecord } from '@/api/member/usageRecord';
import { UsageRecordVO, UsageRecordQuery, UsageRecordForm } from '@/api/member/usageRecord/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const usageRecordList = ref<UsageRecordVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const usageRecordFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: UsageRecordForm = {
  id: undefined,
  memberId: undefined,
  orderNo: undefined,
  ruleId: undefined,
  ruleName: undefined,
  ruleType: undefined,
  originalAmount: undefined,
  discountAmount: undefined,
  finalAmount: undefined,
  applicableProducts: undefined,
  usageTime: undefined,
  status: undefined,
  remark: undefined
};
const data = reactive<PageData<UsageRecordForm, UsageRecordQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    memberId: undefined,
    orderNo: undefined,
    ruleId: undefined,
    ruleName: undefined,
    ruleType: undefined,
    originalAmount: undefined,
    discountAmount: undefined,
    finalAmount: undefined,
    applicableProducts: undefined,
    usageTime: undefined,
    status: undefined,
    params: {}
  },
  rules: {
    applicableProducts: [{ required: true, message: '适用商品信息不能为空', trigger: 'blur' }],
    usageTime: [{ required: true, message: '使用时间不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '状态：1-已使用，2-已退款不能为空', trigger: 'change' }],
    remark: [{ required: true, message: '备注说明不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询优惠使用记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listUsageRecord(queryParams.value);
  usageRecordList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  usageRecordFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: UsageRecordVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加优惠使用记录';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: UsageRecordVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getUsageRecord(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改优惠使用记录';
};

/** 提交按钮 */
const submitForm = () => {
  usageRecordFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateUsageRecord(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addUsageRecord(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: UsageRecordVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除优惠使用记录编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delUsageRecord(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'member/usageRecord/export',
    {
      ...queryParams.value
    },
    `usageRecord_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
