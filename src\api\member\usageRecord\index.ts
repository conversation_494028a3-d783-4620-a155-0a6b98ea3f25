import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { UsageRecordVO, UsageRecordForm, UsageRecordQuery } from '@/api/member/usageRecord/types';

/**
 * 查询优惠使用记录列表
 * @param query
 * @returns {*}
 */

export const listUsageRecord = (query?: UsageRecordQuery): AxiosPromise<UsageRecordVO[]> => {
  return request({
    url: '/member/usageRecord/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询优惠使用记录详细
 * @param id
 */
export const getUsageRecord = (id: string | number): AxiosPromise<UsageRecordVO> => {
  return request({
    url: '/member/usageRecord/' + id,
    method: 'get'
  });
};

/**
 * 新增优惠使用记录
 * @param data
 */
export const addUsageRecord = (data: UsageRecordForm) => {
  return request({
    url: '/member/usageRecord',
    method: 'post',
    data: data
  });
};

/**
 * 修改优惠使用记录
 * @param data
 */
export const updateUsageRecord = (data: UsageRecordForm) => {
  return request({
    url: '/member/usageRecord',
    method: 'put',
    data: data
  });
};

/**
 * 删除优惠使用记录
 * @param id
 */
export const delUsageRecord = (id: string | number | Array<string | number>) => {
  return request({
    url: '/member/usageRecord/' + id,
    method: 'delete'
  });
};
