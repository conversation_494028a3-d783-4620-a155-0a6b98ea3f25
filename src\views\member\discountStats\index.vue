<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="会员ID" prop="memberId">
              <el-input v-model="queryParams.memberId" placeholder="请输入会员ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="规则ID" prop="ruleId">
              <el-input v-model="queryParams.ruleId" placeholder="请输入规则ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="已使用次数" prop="usedCount">
              <el-input v-model="queryParams.usedCount" placeholder="请输入已使用次数" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="最后使用时间" prop="lastUsedTime">
              <el-date-picker clearable v-model="queryParams.lastUsedTime" type="date" value-format="YYYY-MM-DD" placeholder="请选择最后使用时间" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['member:discountStats:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['member:discountStats:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['member:discountStats:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['member:discountStats:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="discountStatsList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键ID" align="center" prop="id" v-if="true" />
        <el-table-column label="会员ID" align="center" prop="memberId" />
        <el-table-column label="规则ID" align="center" prop="ruleId" />
        <el-table-column label="已使用次数" align="center" prop="usedCount" />
        <el-table-column label="最后使用时间" align="center" prop="lastUsedTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.lastUsedTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['member:discountStats:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['member:discountStats:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改会员优惠使用统计对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="discountStatsFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="会员ID" prop="memberId">
          <el-input v-model="form.memberId" placeholder="请输入会员ID" />
        </el-form-item>
        <el-form-item label="规则ID" prop="ruleId">
          <el-input v-model="form.ruleId" placeholder="请输入规则ID" />
        </el-form-item>
        <el-form-item label="已使用次数" prop="usedCount">
          <el-input v-model="form.usedCount" placeholder="请输入已使用次数" />
        </el-form-item>
        <el-form-item label="最后使用时间" prop="lastUsedTime">
          <el-date-picker clearable v-model="form.lastUsedTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择最后使用时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DiscountStats" lang="ts">
import { listDiscountStats, getDiscountStats, delDiscountStats, addDiscountStats, updateDiscountStats } from '@/api/member/discountStats';
import { DiscountStatsVO, DiscountStatsQuery, DiscountStatsForm } from '@/api/member/discountStats/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const discountStatsList = ref<DiscountStatsVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const discountStatsFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: DiscountStatsForm = {
  id: undefined,
  memberId: undefined,
  ruleId: undefined,
  usedCount: undefined,
  lastUsedTime: undefined
};
const data = reactive<PageData<DiscountStatsForm, DiscountStatsQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    memberId: undefined,
    ruleId: undefined,
    usedCount: undefined,
    lastUsedTime: undefined,
    params: {}
  },
  rules: {
    usedCount: [{ required: true, message: '已使用次数不能为空', trigger: 'blur' }],
    lastUsedTime: [{ required: true, message: '最后使用时间不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询会员优惠使用统计列表 */
const getList = async () => {
  loading.value = true;
  const res = await listDiscountStats(queryParams.value);
  discountStatsList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  discountStatsFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: DiscountStatsVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加会员优惠使用统计';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: DiscountStatsVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getDiscountStats(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改会员优惠使用统计';
};

/** 提交按钮 */
const submitForm = () => {
  discountStatsFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateDiscountStats(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addDiscountStats(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: DiscountStatsVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除会员优惠使用统计编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delDiscountStats(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'member/discountStats/export',
    {
      ...queryParams.value
    },
    `discountStats_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
