<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="会员ID" prop="memberId">
              <el-input v-model="queryParams.memberId" placeholder="请输入会员ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="累计消费金额" prop="totalConsumeAmount">
              <el-input v-model="queryParams.totalConsumeAmount" placeholder="请输入累计消费金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="累计消费次数" prop="totalConsumeCount">
              <el-input v-model="queryParams.totalConsumeCount" placeholder="请输入累计消费次数" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="累计充值金额" prop="totalRechargeAmount">
              <el-input v-model="queryParams.totalRechargeAmount" placeholder="请输入累计充值金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="累计充值次数" prop="totalRechargeCount">
              <el-input v-model="queryParams.totalRechargeCount" placeholder="请输入累计充值次数" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="最后消费时间" prop="lastConsumeTime">
              <el-date-picker
                clearable
                v-model="queryParams.lastConsumeTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择最后消费时间"
              />
            </el-form-item>
            <el-form-item label="最后充值时间" prop="lastRechargeTime">
              <el-date-picker
                clearable
                v-model="queryParams.lastRechargeTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择最后充值时间"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['member:statistics:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['member:statistics:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['member:statistics:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['member:statistics:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="statisticsList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键ID" align="center" prop="id" v-if="true" />
        <el-table-column label="会员ID" align="center" prop="memberId" />
        <el-table-column label="累计消费金额" align="center" prop="totalConsumeAmount" />
        <el-table-column label="累计消费次数" align="center" prop="totalConsumeCount" />
        <el-table-column label="累计充值金额" align="center" prop="totalRechargeAmount" />
        <el-table-column label="累计充值次数" align="center" prop="totalRechargeCount" />
        <el-table-column label="最后消费时间" align="center" prop="lastConsumeTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.lastConsumeTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="最后充值时间" align="center" prop="lastRechargeTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.lastRechargeTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['member:statistics:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['member:statistics:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改会员统计对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="statisticsFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="会员ID" prop="memberId">
          <el-input v-model="form.memberId" placeholder="请输入会员ID" />
        </el-form-item>
        <el-form-item label="累计消费金额" prop="totalConsumeAmount">
          <el-input v-model="form.totalConsumeAmount" placeholder="请输入累计消费金额" />
        </el-form-item>
        <el-form-item label="累计消费次数" prop="totalConsumeCount">
          <el-input v-model="form.totalConsumeCount" placeholder="请输入累计消费次数" />
        </el-form-item>
        <el-form-item label="累计充值金额" prop="totalRechargeAmount">
          <el-input v-model="form.totalRechargeAmount" placeholder="请输入累计充值金额" />
        </el-form-item>
        <el-form-item label="累计充值次数" prop="totalRechargeCount">
          <el-input v-model="form.totalRechargeCount" placeholder="请输入累计充值次数" />
        </el-form-item>
        <el-form-item label="最后消费时间" prop="lastConsumeTime">
          <el-date-picker
            clearable
            v-model="form.lastConsumeTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择最后消费时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="最后充值时间" prop="lastRechargeTime">
          <el-date-picker
            clearable
            v-model="form.lastRechargeTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择最后充值时间"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Statistics" lang="ts">
import { listStatistics, getStatistics, delStatistics, addStatistics, updateStatistics } from '@/api/member/statistics';
import { StatisticsVO, StatisticsQuery, StatisticsForm } from '@/api/member/statistics/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const statisticsList = ref<StatisticsVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const statisticsFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: StatisticsForm = {
  id: undefined,
  memberId: undefined,
  totalConsumeAmount: undefined,
  totalConsumeCount: undefined,
  totalRechargeAmount: undefined,
  totalRechargeCount: undefined,
  lastConsumeTime: undefined,
  lastRechargeTime: undefined
};
const data = reactive<PageData<StatisticsForm, StatisticsQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    memberId: undefined,
    totalConsumeAmount: undefined,
    totalConsumeCount: undefined,
    totalRechargeAmount: undefined,
    totalRechargeCount: undefined,
    lastConsumeTime: undefined,
    lastRechargeTime: undefined,
    params: {}
  },
  rules: {
    totalConsumeAmount: [{ required: true, message: '累计消费金额不能为空', trigger: 'blur' }],
    totalConsumeCount: [{ required: true, message: '累计消费次数不能为空', trigger: 'blur' }],
    totalRechargeAmount: [{ required: true, message: '累计充值金额不能为空', trigger: 'blur' }],
    totalRechargeCount: [{ required: true, message: '累计充值次数不能为空', trigger: 'blur' }],
    lastConsumeTime: [{ required: true, message: '最后消费时间不能为空', trigger: 'blur' }],
    lastRechargeTime: [{ required: true, message: '最后充值时间不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询会员统计列表 */
const getList = async () => {
  loading.value = true;
  const res = await listStatistics(queryParams.value);
  statisticsList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  statisticsFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: StatisticsVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加会员统计';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: StatisticsVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getStatistics(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改会员统计';
};

/** 提交按钮 */
const submitForm = () => {
  statisticsFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateStatistics(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addStatistics(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: StatisticsVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除会员统计编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delStatistics(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'member/statistics/export',
    {
      ...queryParams.value
    },
    `statistics_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
