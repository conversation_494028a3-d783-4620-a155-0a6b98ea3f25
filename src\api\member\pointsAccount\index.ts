import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PointsAccountVO, PointsAccountForm, PointsAccountQuery } from '@/api/member/pointsAccount/types';

/**
 * 查询会员积分账户列表
 * @param query
 * @returns {*}
 */

export const listPointsAccount = (query?: PointsAccountQuery): AxiosPromise<PointsAccountVO[]> => {
  return request({
    url: '/member/pointsAccount/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询会员积分账户详细
 * @param id
 */
export const getPointsAccount = (id: string | number): AxiosPromise<PointsAccountVO> => {
  return request({
    url: '/member/pointsAccount/' + id,
    method: 'get'
  });
};

/**
 * 新增会员积分账户
 * @param data
 */
export const addPointsAccount = (data: PointsAccountForm) => {
  return request({
    url: '/member/pointsAccount',
    method: 'post',
    data: data
  });
};

/**
 * 修改会员积分账户
 * @param data
 */
export const updatePointsAccount = (data: PointsAccountForm) => {
  return request({
    url: '/member/pointsAccount',
    method: 'put',
    data: data
  });
};

/**
 * 删除会员积分账户
 * @param id
 */
export const delPointsAccount = (id: string | number | Array<string | number>) => {
  return request({
    url: '/member/pointsAccount/' + id,
    method: 'delete'
  });
};
