import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { StatisticsVO, StatisticsForm, StatisticsQuery } from '@/api/member/statistics/types';

/**
 * 查询会员统计列表
 * @param query
 * @returns {*}
 */

export const listStatistics = (query?: StatisticsQuery): AxiosPromise<StatisticsVO[]> => {
  return request({
    url: '/member/statistics/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询会员统计详细
 * @param id
 */
export const getStatistics = (id: string | number): AxiosPromise<StatisticsVO> => {
  return request({
    url: '/member/statistics/' + id,
    method: 'get'
  });
};

/**
 * 新增会员统计
 * @param data
 */
export const addStatistics = (data: StatisticsForm) => {
  return request({
    url: '/member/statistics',
    method: 'post',
    data: data
  });
};

/**
 * 修改会员统计
 * @param data
 */
export const updateStatistics = (data: StatisticsForm) => {
  return request({
    url: '/member/statistics',
    method: 'put',
    data: data
  });
};

/**
 * 删除会员统计
 * @param id
 */
export const delStatistics = (id: string | number | Array<string | number>) => {
  return request({
    url: '/member/statistics/' + id,
    method: 'delete'
  });
};
