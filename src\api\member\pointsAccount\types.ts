export interface PointsAccountVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 会员ID
   */
  memberId: string | number;

  /**
   * 会员名称
   */
  memberName: string;

  /**
   * 总积分
   */
  totalPoints: number;

  /**
   * 可用积分
   */
  availablePoints: number;
}

export interface PointsAccountForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 会员ID
   */
  memberId?: string | number;

  /**
   * 会员名称
   */
  memberName?: string;

  /**
   * 总积分
   */
  totalPoints?: number;

  /**
   * 可用积分
   */
  availablePoints?: number;
}

export interface PointsAccountQuery extends PageQuery {
  /**
   * 会员ID
   */
  memberId?: string | number;

  /**
   * 会员名称
   */
  memberName?: string;

  /**
   * 总积分
   */
  totalPoints?: number;

  /**
   * 可用积分
   */
  availablePoints?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
